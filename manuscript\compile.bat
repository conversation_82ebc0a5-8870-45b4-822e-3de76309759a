@echo off
REM Batch script for compiling LaTeX manuscript on Windows

echo Compiling manuscript...

REM First LaTeX run
echo Running pdflatex (1/3)...
pdflatex -interaction=nonstopmode main.tex

REM BibTeX run
echo Running bibtex...
bibtex main

REM Second LaTeX run
echo Running pdflatex (2/3)...
pdflatex -interaction=nonstopmode main.tex

REM Third LaTeX run
echo Running pdflatex (3/3)...
pdflatex -interaction=nonstopmode main.tex

echo Compilation complete!
echo Output: main.pdf

REM Clean up auxiliary files (optional)
set /p cleanup="Clean up auxiliary files? (y/n): "
if /i "%cleanup%"=="y" (
    echo Cleaning up...
    del *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz 2>nul
    echo Cleanup complete!
)

pause
