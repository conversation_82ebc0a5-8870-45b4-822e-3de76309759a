#!/bin/bash
# Shell script for compiling LaTeX manuscript on Unix/Linux/macOS

echo "Compiling manuscript..."

# First LaTeX run
echo "Running pdflatex (1/3)..."
pdflatex -interaction=nonstopmode main.tex

# BibTeX run
echo "Running bibtex..."
bibtex main

# Second LaTeX run
echo "Running pdflatex (2/3)..."
pdflatex -interaction=nonstopmode main.tex

# Third LaTeX run
echo "Running pdflatex (3/3)..."
pdflatex -interaction=nonstopmode main.tex

echo "Compilation complete!"
echo "Output: main.pdf"

# Clean up auxiliary files (optional)
read -p "Clean up auxiliary files? (y/n): " cleanup
if [[ $cleanup == "y" || $cleanup == "Y" ]]; then
    echo "Cleaning up..."
    rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot *.fls *.fdb_latexmk *.synctex.gz
    echo "Cleanup complete!"
fi
