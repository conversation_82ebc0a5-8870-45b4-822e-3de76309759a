This is BibTeX, Version 0.99d
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: main.aux
Reallocating 'name_of_file' (item size: 1) to 4 items.
The style file: agu.bst
Reallocating 'name_of_file' (item size: 1) to 11 items.
Database file #1: references.bib
Warning--I didn't find a database entry for "Sivjee1987"
Warning--I didn't find a database entry for "Walterscheid1987"
Warning--I didn't find a database entry for "Schubert1991"
Warning--I didn't find a database entry for "Tarasick1992"
Warning--I didn't find a database entry for "Russell1999"
Warning--I didn't find a database entry for "She2019"
Warning--I didn't find a database entry for "Zhao2020"
Warning--I didn't find a database entry for "Offermann2011"
Warning--I didn't find a database entry for "Nikoukar2007"
Warning--I didn't find a database entry for "Sato2009"
Warning--I didn't find a database entry for "Vincent2013"
Warning--I didn't find a database entry for "Bramberger2017"
Warning--I didn't find a database entry for "Zhang2012"
Warning--I didn't find a database entry for "Ern2011"
Warning--I didn't find a database entry for "Mze2014"
Warning--I didn't find a database entry for "Lu2015"
Warning--I didn't find a database entry for "Snively2013"
Warning--I didn't find a database entry for "Tang2014"
Warning--I didn't find a database entry for "Nielsen2012"
Warning--I didn't find a database entry for "Thurairajah2020"
Warning--I didn't find a database entry for "Liu2017"
Warning--I didn't find a database entry for "Beig2003"
Warning--I didn't find a database entry for "Lieberman2013"
Warning--I didn't find a database entry for "Li2016"
Reallocating 'wiz_functions' (item size: 4) to 6000 items.
You've used 0 entries,
            3075 wiz_defined-function locations,
            1110 strings with 9738 characters,
and the built_in function-call counts, 29 in all, are:
= -- 0
> -- 0
< -- 0
+ -- 0
- -- 0
* -- 2
:= -- 8
add.period$ -- 0
call.type$ -- 0
change.case$ -- 0
chr.to.int$ -- 0
cite$ -- 0
duplicate$ -- 0
empty$ -- 1
format.name$ -- 0
if$ -- 1
int.to.chr$ -- 1
int.to.str$ -- 1
missing$ -- 0
newline$ -- 7
num.names$ -- 0
pop$ -- 0
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 1
stack$ -- 0
substring$ -- 0
swap$ -- 0
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 0
width$ -- 0
write$ -- 6
(There were 24 warnings)
