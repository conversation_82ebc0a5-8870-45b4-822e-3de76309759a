This is pdfTeX, Version 3.141592653-2.6-1.40.26 (MiKTeX 24.4) (preloaded format=pdflatex 2024.8.5)  1 JUN 2025 01:17
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./main.tex
(main.tex
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2024/02/08 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2024/02/08 v1.4n Standard LaTeX file (size option)
)
\c@part=\count194
\c@section=\count195
\c@subsection=\count196
\c@subsubsection=\count197
\c@paragraph=\count198
\c@subparagraph=\count199
\c@figure=\count266
\c@table=\count267
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.s
ty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count268
\Gm@cntv=\count269
\c@Gm@tempcnt=\count270
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.c
fg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/05/23 v2.17q AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen150
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen151
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.st
y
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count271
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count272
\leftroot@=\count273
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count274
\DOTSCASE@=\count275
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen152
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count276
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count277
\dotsspace@=\muskip17
\c@parentequation=\count278
\dspbrk@lvl=\count279
\tag@help=\toks20
\row@=\count280
\column@=\count281
\maxfields@=\count282
\andhelp@=\toks21
\eqnshift@=\dimen153
\alignsep@=\dimen154
\tagshift@=\dimen155
\tagwidth@=\dimen156
\totwidth@=\dimen157
\lineht@=\dimen158
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.st
y
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.s
ty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.s
ty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.s
ty
Package: graphics 2024/05/23 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphi
cs.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex
.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen159
\Gin@req@width=\dimen160
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/natbib\natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip54
\bibsep=\skip55
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count283
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/lineno\lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.s
ty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count284
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions
.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.s
ty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys
.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
\linenopenalty=\count285
\output=\toks24
\linenoprevgraf=\count286
\linenumbersep=\dimen161
\linenumberwidth=\dimen162
\c@linenumber=\count287
\c@pagewiselinenumber=\count288
\c@LN@truepage=\count289
\c@internallinenumber=\count290
\c@internallinenumbers=\count291
\quotelinenumbersep=\dimen163
\bframerule=\dimen164
\bframesep=\dimen165
\bframebox=\box54
\linenoamsmath@ams@eqpen=\count292
LaTeX Info: Redefining \\ on input line 3180.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.s
ty
Package: setspace 2022/12/04 v6.7b set line spacing
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\color.sty
Package: color 2024/01/14 v1.3d Standard LaTeX Color (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.
cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.
ltx))
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 2
5.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend
-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count293
\l__pdf_internal_box=\box55
) (main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 25.
LaTeX Font Info:    ... okay on input line 25.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pd
f.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count294
\scratchdimen=\dimen166
\scratchbox=\box56
\nofMPsegments=\count295
\nofMParguments=\count296
\everyMPshowfont=\toks25
\MPscratchCnt=\count297
\MPscratchDim=\dimen167
\MPnumerator=\count298
\makeMPintoPDFobject=\count299
\everyMPtoPDFconversion=\toks26
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstop
df-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-s
ys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
LaTeX Font Info:    Trying to load font information for OT1+phv on input line 2
7.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/psnfss\ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 27.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 27.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
Overfull \hbox (22.0407pt too wide) in paragraph at lines 30--31
\OT1/ptm/m/n/10.95 OH emis-sion and grav-ity wave po-ten-tial en-ergy (Ep) us-i
ng 21 years (2002-2023) of TIMED/SABER
 []


! LaTeX Error: Unicode character ₃ (U+2083)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.37 ...etween atomic hydrogen and ozone (H + O₃
                                                   → OH* + O₂), has been...

You may provide a definition with
\DeclareUnicodeCharacter 

LaTeX Info: Symbol \textrightarrow not provided by
            font family ptm in TS1 encoding.
            Default family used instead on input line 37.

! LaTeX Error: Unicode character ₂ (U+2082)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.37 ...ydrogen and ozone (H + O₃ → OH* + O₂
                                                  ), has been extensively us...

You may provide a definition with
\DeclareUnicodeCharacter 


Package natbib Warning: Citation `Sivjee1987' on page 1 undefined on input line
 37.


Package natbib Warning: Citation `Walterscheid1987' on page 1 undefined on inpu
t line 37.


Package natbib Warning: Citation `Schubert1991' on page 1 undefined on input li
ne 37.


Package natbib Warning: Citation `Tarasick1992' on page 1 undefined on input li
ne 37.


Package natbib Warning: Citation `Russell1999' on page 1 undefined on input lin
e 37.


Package natbib Warning: Citation `She2019' on page 1 undefined on input line 37
.


Package natbib Warning: Citation `Zhao2020' on page 1 undefined on input line 3
7.


Package natbib Warning: Citation `Offermann2011' on page 1 undefined on input l
ine 37.


Package natbib Warning: Citation `Nikoukar2007' on page 1 undefined on input li
ne 37.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{C:/Users/
ToyeTunde/AppData/Local/Programs/MiKTeX/fonts/enc/dvips/base/8r.enc}]

Package natbib Warning: Citation `Sato2009' on page 2 undefined on input line 3
9.


Package natbib Warning: Citation `Vincent2013' on page 2 undefined on input lin
e 39.


Package natbib Warning: Citation `Bramberger2017' on page 2 undefined on input 
line 39.


Package natbib Warning: Citation `Zhang2012' on page 2 undefined on input line 
39.


Package natbib Warning: Citation `Ern2011' on page 2 undefined on input line 39
.


Package natbib Warning: Citation `Mzé2014' on page 2 undefined on input line 3
9.


Package natbib Warning: Citation `Lu2015' on page 2 undefined on input line 39.



Package natbib Warning: Citation `Snively2013' on page 2 undefined on input lin
e 39.


Package natbib Warning: Citation `Tang2014' on page 2 undefined on input line 3
9.


Package natbib Warning: Citation `Nielsen2012' on page 2 undefined on input lin
e 39.


Package natbib Warning: Citation `Thurairajah2020' on page 2 undefined on input
 line 39.


Package natbib Warning: Citation `Liu2017' on page 2 undefined on input line 39
.



[2]

Package natbib Warning: Citation `Beig2003' on page 3 undefined on input line 4
1.


Package natbib Warning: Citation `Lieberman2013' on page 3 undefined on input l
ine 41.


Package natbib Warning: Citation `Li2016' on page 3 undefined on input line 41.



Overfull \hbox (43.48055pt too wide) in paragraph at lines 45--46
\OT1/ptm/m/n/12 meso-spheric OH emis-sion and grav-ity wave po-ten-tial en-ergy
 us-ing 21 years (2002-2023) of TIMED/SABER
 []



[3]

[4]
No file main.bbl.

Package natbib Warning: There were undefined citations.



[5] (main.aux)
 ***********
LaTeX2e <2024-06-01> patch level 2
L3 programming layer <2024-05-27>
 ***********
 ) 
Here is how much of TeX's memory you used:
 4702 strings out of 473904
 71171 string characters out of 5724713
 1936908 words of memory out of 5000000
 27507 multiletter control sequences out of 15000+600000
 575295 words of font info for 67 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,8n,65p,1618b,310s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\
tcrm1200.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/publi
c/amsfonts/cm/cmr10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts
/type1/urw/helvetic/uhvr8a.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTe
X/fonts/type1/urw/times/utmb8a.pfb><C:/Users/<USER>/AppData/Local/Programs/M
iKTeX/fonts/type1/urw/times/utmr8a.pfb>
Output written on main.pdf (5 pages, 58827 bytes).
PDF statistics:
 44 PDF objects out of 1000 (max. 8388607)
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

